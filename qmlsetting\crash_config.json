{"crashHandler": {"enabled": true, "dumpPath": "crashes", "dumpType": "MiniDumpWithDataSegs", "enableLogging": true, "maxDumpFiles": 50, "dumpFileRetentionDays": 30, "autoCleanup": true, "compressionEnabled": false, "uploadEnabled": false, "uploadUrl": "", "uploadApiKey": ""}, "dumpTypes": {"MiniDump": {"description": "最小转储，包含基本信息", "size": "小", "debugInfo": "基本"}, "MiniDumpWithDataSegs": {"description": "包含数据段的小转储", "size": "中等", "debugInfo": "详细"}, "MiniDumpWithFullMemory": {"description": "完整内存转储", "size": "大", "debugInfo": "完整"}, "MiniDumpWithHandleData": {"description": "包含句柄数据的转储", "size": "中等", "debugInfo": "详细"}, "MiniDumpFilterMemory": {"description": "过滤内存转储", "size": "中等", "debugInfo": "过滤"}, "MiniDumpScanMemory": {"description": "扫描内存转储", "size": "中等", "debugInfo": "扫描"}}, "logging": {"crashLoggerName": "crash_logger", "logLevel": "CRITICAL", "logFormat": "%^[CRASH %Y-%m-%d %H:%M:%S.%e] <thread %t> [%l]\n%@,%!\n%v%$\n", "logToFile": true, "logToConsole": true, "crashLogFile": "log/crash.log", "maxLogFileSize": 10485760, "maxLogFiles": 5}, "recovery": {"enabled": true, "autoRestart": false, "restartDelay": 5000, "maxRestartAttempts": 3, "saveStateOnCrash": true, "restoreStateOnRestart": true}, "notification": {"showCrashDialog": true, "allowUserReport": true, "collectUserFeedback": true, "emailReport": false, "emailAddress": "", "includeSystemInfo": true, "includeApplicationLog": true}, "debugging": {"generateTestCrash": false, "enableStackTrace": true, "enableSymbolResolution": true, "symbolPath": "", "enableMemoryDump": true, "enableRegisterDump": true}, "performance": {"maxDumpGenerationTime": 30000, "enableAsyncDumpGeneration": true, "priorityClass": "NORMAL", "threadPriority": "NORMAL"}}