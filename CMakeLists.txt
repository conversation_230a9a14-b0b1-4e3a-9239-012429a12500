﻿cmake_minimum_required(VERSION 3.14)

project(IDEV1_MainApp VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(CMAKE_INCLUDE_CURRENT_DIR ON)

list(APPEND QML_DIRS "${PROJECT_SOURCE_DIR}/modules")
set(QML_IMPORT_PATH "${QML_DIRS}" CACHE STRING "qml import paths")
set(QML_DESIGNER_IMPORT_PATH "${QML_DIRS}" CACHE STRING "qml designer import paths")

# 设置版本号
set(VERSION_NUMBER "v2.0.15")

# 将版本号作为宏定义传递给编译器
add_definitions(-DVERSION_NUMBER="${VERSION_NUMBER}")

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core Concurrent Svg Xml Quick Widgets QuickWidgets PrintSupport Sql
    WebChannel WebSockets WebView WebEngine WebEngineWidgets)

find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Concurrent Svg Xml Quick Widgets QuickWidgets PrintSupport Sql
    WebChannel WebSockets WebView WebEngine WebEngineWidgets)

if(NOT TARGET kddockwidgets)
    find_package(KDDockWidgets REQUIRED)
endif()

#find_package(KDDockWidgets REQUIRED)

include_directories(include)
include_directories(include/QxOrm/include)
include_directories(include/spdlog/include)
include_directories(include/QXlsx/include)

link_directories(lib)

set(CMAKE_INSTALL_RPATH "${CMAKE_INSTALL_PREFIX}/path/to/QtWebEngine/lib")

set(PROJECT_SOURCES
        main.cpp
        qmlclass/debugmanage.h
        qmlclass/debugmanage.cpp
        qmlclass/wavemanage.h
        qmlclass/wavemanage.cpp
        qmlclass/wavetriggerset.h
        qmlclass/wavetriggerset.cpp
        qmlclass/wavepointset.h
        qmlclass/wavepointset.cpp
        qmlclass/waveset.h
        qmlclass/waveset.cpp
        qmlclass/serviceinterface.h
        qmlclass/serviceinterface.cpp
        qmlclass/appsettings.h
        qmlclass/appsettings.cpp
        qmlclass/dataentrymodel.h
        qmlclass/dataentrymodel.cpp
        qmlclass/roleentrymodel.h
        qmlclass/roleentrymodel.cpp
        qmlclass/sentjsontoqml.h
        qmlclass/sentjsontoqml.cpp
        qmlclass/testdsdata.cpp
        qmlclass/testdsdata.h
        qmlclass/Trans.h
        qmlclass/Trans.cpp
        qmlclass/FileReadWrite.h
        qmlclass/json.hpp
        qmlclass/dictmanager.h
        qmlclass/dictmanager.cpp
        qmlclass/menumanager.h
        qmlclass/menumanager.cpp
        qmlclass/toolbarmanager.h
        qmlclass/toolbarmanager.cpp
        qmlclass/steditormanager.h
        qmlclass/steditormanager.cpp
        qmlclass/readcodeprocess.h
        qmlclass/readcodeprocess.cpp

        qmlclass/fileutils.h
        qmlclass/fileutils.cpp
        qmlclass/qtpdfviewerinitializer.h
        qmlclass/qtpdfviewerinitializer.cpp
        qmlclass/singleton.h
        qmlclass/singleton.cpp
        qmlclass/WebSocketTransport.h
        qmlclass/monitor.h
        qmlclass/monitor.cpp
        qmlclass/debugfunc.h
        qmlclass/debugfunc.cpp
        qmlclass/crashhandler.h
        qmlclass/crashhandler.cpp

        SimpleUI/cpp/SimpleUI.h
        SimpleUI/cpp/SimpleUI.cpp
        SimpleUI/cpp/TreeTableNode.h
        SimpleUI/cpp/TreeTableNode.cpp
        SimpleUI/cpp/TreeTableModel.h
        SimpleUI/cpp/TreeTableModel.cpp

        SimpleUI/cpp/TreeNode.h
        SimpleUI/cpp/TreeNode.cpp
        SimpleUI/cpp/TreeModel.h
        SimpleUI/cpp/TreeModel.cpp

        qml.qrc
        help.pdf

        qmlsetting/dict.json
        qmlsetting/devicetree.json
        qmlsetting/keys.json
        qmlsetting/dt_interface_eth.json
        qmlsetting/dt_interface_serial.json
        qmlsetting/dt_interface_sd.json
        qmlsetting/dt_baseplane.json
        qmlsetting/language_zh.json
        qmlsetting/menu.json
        qmlsetting/toolbar.json
        qmlsetting/code_process_config.json
        qmlsetting/usermanage.json
        qmlsetting/log_config.json
        qmlsetting/crash_config.json
        qmlsetting/config.ini
        qmlsetting/template.db
        qmlsetting/soe.csv
        icon.rc
        module_test.h
        ${TS_FILES}
)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(IDEV1_MainApp
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )
# Define target properties for Android with Qt 6 as:
#    set_property(TARGET IDEV1_Main APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
#                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
# For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    if(ANDROID)
        add_library(IDEV1_MainApp SHARED
            ${PROJECT_SOURCES}
        )
# Define properties for Android with Qt 5 after find_package() calls as:
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(IDEV1_MainApp
          ${PROJECT_SOURCES}
          "icon.rc"
        )
    endif()
endif()

add_subdirectory(HttpServer)
add_subdirectory(OnlineAccess)
add_subdirectory(ToolBox)
add_subdirectory(WaveRecord)
add_subdirectory(CompileHub)
add_subdirectory(CommServ)
add_subdirectory(FileProcess)

# set_target_properties(FileProcess PROPERTIES ENABLE_EXPORTS TRUE)

target_link_libraries(IDEV1_MainApp
  PRIVATE Qt${QT_VERSION_MAJOR}::Core
  Qt${QT_VERSION_MAJOR}::Quick
  Qt${QT_VERSION_MAJOR}::Concurrent
  Qt${QT_VERSION_MAJOR}::Widgets
  Qt${QT_VERSION_MAJOR}::QuickWidgets
  Qt${QT_VERSION_MAJOR}::Xml
  Qt${QT_VERSION_MAJOR}::PrintSupport
  Qt${QT_VERSION_MAJOR}::Sql
  Qt${QT_VERSION_MAJOR}::Svg
  Qt${QT_VERSION_MAJOR}::WebChannel
  Qt${QT_VERSION_MAJOR}::WebSockets
  Qt${QT_VERSION_MAJOR}::WebView
  Qt${QT_VERSION_MAJOR}::WebEngine
  Qt${QT_VERSION_MAJOR}::WebEngineWidgets
  Qt${QT_VERSION_MAJOR}::GuiPrivate
  KDAB::kddockwidgets
  PRIVATE HttpServer
  PUBLIC IDECommon
  PUBLIC IDEBControl2
  PUBLIC IDEProjectAndFile2
  PUBLIC IDEVariable2
  PUBLIC IDEDeviceAndNetwork2
  PUBLIC IDEUserAndGroup2
  PUBLIC IDESOEAndTrack2
  PUBLIC IDEOutput2
  PUBLIC IDEOnlineServer2
  PUBLIC IDECFCEditor2
  PUBLIC IDEFBDEditor2
  PUBLIC IDESTEditor2
  PUBLIC IDELDEditor2
  #PUBLIC CommServ
  PRIVATE subdir_online
  PRIVATE subdir_toolbox
  PRIVATE subdir_waverecord
  PRIVATE subdir_compilehub
  PRIVATE subdir_library
)

# Windows特定的库依赖
if(WIN32)
    target_link_libraries(IDEV1_MainApp PRIVATE dbghelp)
endif()

set_target_properties(IDEV1_MainApp PROPERTIES
    MACOSX_BUNDLE_GUI_IDENTIFIER my.example.com
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
    # FileProcess PROPERTIES ENABLE_EXPORTS TRUE
)

install(TARGETS IDEV1_MainApp
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_import_qml_plugins(IDEV1_MainApp)
    qt_finalize_executable(IDEV1_MainApp)
endif()


# 将配置文件 字典 工具栏 菜单栏 的JSON文件拷贝到app目录下
configure_file(qmlsetting/template.db ${CMAKE_CURRENT_BINARY_DIR}/Settings/template.db COPYONLY)
configure_file(qmlsetting/config.ini ${CMAKE_CURRENT_BINARY_DIR}/Settings/config.ini COPYONLY)
#configure_file(qmlsetting/devicetree.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/devicetree.json COPYONLY)
configure_file(qmlsetting/dict.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/dict.json COPYONLY)
configure_file(qmlsetting/menu.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/menu.json COPYONLY)
configure_file(qmlsetting/toolbar.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/toolbar.json COPYONLY)
configure_file(qmlsetting/soe.csv ${CMAKE_CURRENT_BINARY_DIR}/Settings/soe.csv COPYONLY)
#configure_file(qmlsetting/usermanage.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/usermanage.json COPYONLY)
configure_file(qmlsetting/keys.json ${CMAKE_CURRENT_BINARY_DIR}/Trans/keys.json COPYONLY)
configure_file(qmlsetting/language_zh.json ${CMAKE_CURRENT_BINARY_DIR}/Trans/language_zh.json COPYONLY)
configure_file(qmlsetting/package.bat ${CMAKE_CURRENT_BINARY_DIR}/Settings/package.bat COPYONLY)
#configure_file(qmlsetting/start.bat ${CMAKE_CURRENT_BINARY_DIR}/editor/start.bat COPYONLY)
#configure_file(qmlsetting/stop.bat ${CMAKE_CURRENT_BINARY_DIR}/editor/stop.bat COPYONLY)
configure_file(qmlsetting/clientManeger.bat ${CMAKE_CURRENT_BINARY_DIR}/Settings/clientManeger.bat COPYONLY)
configure_file(qmlsetting/startClientManager.bat ${CMAKE_CURRENT_BINARY_DIR}/Settings/startClientManager.bat COPYONLY)
configure_file(qmlsetting/log_config.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/log_config.json COPYONLY)
configure_file(qmlsetting/crash_config.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/crash_config.json COPYONLY)
configure_file(qmlsetting/code_process_config.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/code_process_config.json COPYONLY)
configure_file(qmlsetting/IEC_STANDARD_FUN_CFCFBD_BL.inc ${CMAKE_CURRENT_BINARY_DIR}/Settings/IEC_STANDARD_FUN_CFCFBD_BL.inc COPYONLY)
configure_file(qmlsetting/GLOBPROT.INC ${CMAKE_CURRENT_BINARY_DIR}/Settings/GLOBPROT.INC COPYONLY)
configure_file(qmlsetting/CompatibleType.xml ${CMAKE_CURRENT_BINARY_DIR}/Settings/CompatibleType.xml COPYONLY)
configure_file(qmlsetting/extend_fbc.xml ${CMAKE_CURRENT_BINARY_DIR}/Settings/extend_fbc.xml COPYONLY)
configure_file(qmlsetting/conf_advance.pk ${CMAKE_CURRENT_BINARY_DIR}/Settings/conf_advance.pk COPYONLY)
configure_file(qmlsetting/conf_mapping.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/conf_mapping.json COPYONLY)
configure_file(qmlsetting/path_dir.json ${CMAKE_CURRENT_BINARY_DIR}/Settings/path_dir.json COPYONLY)
configure_file(qmlsetting/struct_pick.pk ${CMAKE_CURRENT_BINARY_DIR}/Settings/struct_pick.pk COPYONLY)
configure_file(qmlsetting/FileProcess.exe ${CMAKE_CURRENT_BINARY_DIR}/FileProcess.exe COPYONLY)
configure_file(qmlsetting/source1.dat ${CMAKE_CURRENT_BINARY_DIR}/source1.dat COPYONLY)
configure_file(qmlsetting/source2.dat ${CMAKE_CURRENT_BINARY_DIR}/source2.dat COPYONLY)
configure_file(help.pdf ${CMAKE_CURRENT_BINARY_DIR}/help.pdf COPYONLY)

#target_dir（目标目录）cur_dir（当前目录）
macro(COPY_DLL_FILES target_dir cur_dir)
    file(GLOB_RECURSE dll_files "${cur_dir}/*.dll")
    foreach(file ${dll_files})
    file(COPY ${file} DESTINATION ${target_dir})
    endforeach()
endmacro()

# 模板文件复制
file(GLOB allCopyFiles  "qmlsetting/modules/plc_source/*")
file(COPY ${allCopyFiles} DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/modules/plc_source)

file(GLOB allCopyFilesBuild  "qmlsetting/BuildFw/*")
file(COPY ${allCopyFilesBuild} DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/BuildFw)

file(GLOB allCopyFilesBuild  "qmlsetting/code2st_dist/*")
file(COPY ${allCopyFilesBuild} DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/code2st_dist)

file(GLOB allCopyFilesBuild  "qmlsetting/new_c_dist/*")
file(COPY ${allCopyFilesBuild} DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/new_c_dist)

COPY_DLL_FILES("${CMAKE_CURRENT_BINARY_DIR}" "${PROJECT_SOURCE_DIR}/lib")
COPY_DLL_FILES("${CMAKE_CURRENT_BINARY_DIR}" "${PROJECT_SOURCE_DIR}/modules")


# 将{CMAKE_CURRENT_SOURCE_DIR}/HttpServer/httpConfig下所有文件拷贝到Settings目录下
file(GLOB allCopyFiles  "${CMAKE_CURRENT_SOURCE_DIR}/HttpServer/httpConfig/*")
file(COPY ${allCopyFiles} DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/Settings)

